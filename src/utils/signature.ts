/**
 * 签名生成工具
 * 用于生成当贝AI API请求所需的安全签名
 */

import { createHash } from 'crypto';
import { SignatureParams, DeviceConfig } from '../types';

/**
 * 签名生成器类
 * 负责生成API请求的MD5签名
 */
export class SignatureUtils {
  /**
   * 生成API请求签名
   *
   * 注意：经过详尽的逆向工程分析，我们无法完全匹配当贝AI的官方签名算法。
   * 当贝AI可能使用了专有的加密算法、服务器端密钥或其他动态参数。
   * 当前实现提供了一个标准的签名生成方式，但API调用可能会失败。
   * 程序设计了完善的错误处理和本地备用方案来应对这种情况。
   *
   * @param params 签名参数
   * @returns MD5签名字符串（大写）
   */
  public static generateSignature(
    params: SignatureParams,
    extra?: Partial<Pick<DeviceConfig, 'appType' | 'appVersion' | 'clientVersion' | 'lang'>>
  ): string {
    // 新逻辑（与线上 _app chunk 拦截器一致）：
    // sign = MD5( `${timestamp}${O(e,t)}${nonce}` ).toUpperCase()
    // 其中 O(e,t) 为“规范化请求串”：通常由 method + url(+可选query/body规则) 组成。
    // 为避免与线上细节不一致导致失败，我们采取最小可用实现：method + url。

    // 1) 基础字段（必须）
    const { timestamp, nonce, deviceId } = params;

    // 2) 规范化请求串 O(e,t)：
    // - 优先用 params.method/params.url；
    // - 兜底用默认 POST 与 '/ai-search' 起始路径（我们的服务均走该前缀）。
    const method = (params.method || 'POST').toUpperCase();
    let url = params.url || '';
    // 仅取路径部分，去掉协议/域名（如果误传了完整URL）
    if (url.startsWith('http://') || url.startsWith('https://')) {
      try {
        const u = new URL(url);
        url = u.pathname + (u.search || '');
      } catch {
        // 保守降级，直接使用原值
      }
    }
    // 确保路径以 / 开头
    if (url && !url.startsWith('/')) url = '/' + url;

    // O(e,t) 的最小实现：method + ' ' + url
    const normalized = `${method} ${url}`;

    // 3) 组合签名串：timestamp + normalized + nonce
    const signString = `${timestamp}${normalized}${nonce}`;

    // 4) 生成MD5哈希签名（大写格式）
    const signature = createHash('md5')
      .update(signString)
      .digest('hex')
      .toUpperCase();

    // 在调试模式下输出详细的签名信息
    if (process.env['NODE_ENV'] === 'development') {
      console.log('🔐 签名生成详情:');
      console.log('  - 算法类型: 标准MD5哈希');
      console.log('  - 签名字符串:', signString);
      console.log('  - 生成的签名:', signature);
      console.log('  - 参数数量:', sortedKeys.length);
      if (extra) {
        console.log('  - 参与签名的设备/应用信息:', JSON.stringify(extra));
      }
      if (params.data) {
        console.log('  - 请求数据(data):', JSON.stringify(params.data));
        console.log('  - 注意: 请求数据未纳入签名计算');
      }
      console.log('  - ⚠️  提示: 未从前端产物直接定位签名实现，当前实现依据抓包推断');
    }

    return signature;
  }

  /**
   * 生成随机nonce字符串
   *
   * 基于对真实API请求的分析，nonce格式为：
   * - 长度：21个字符
   * - 字符集：数字、大小写字母、连字符(-)
   * - 示例：'111g0amuaFUAic500cMI-'
   *
   * @param length nonce长度，默认为21（与观察到的真实请求一致）
   * @returns 随机nonce字符串
   */
  public static generateNonce(length: number = 21): string {
    // 基于真实API请求分析的字符集：数字、字母、连字符
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-';
    let result = '';

    // 生成随机字符串
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    return result;
  }

  /**
   * 获取当前时间戳（秒）
   * 
   * @returns Unix时间戳
   */
  public static getTimestamp(): number {
    return Math.floor(Date.now() / 1000);
  }

  /**
   * 获取当前时间戳（毫秒）
   * 
   * @returns Unix时间戳（毫秒）
   */
  public static getTimestampMs(): number {
    return Date.now();
  }

  /**
   * 验证签名是否有效
   * 
   * @param signature 待验证的签名
   * @param params 签名参数
   * @returns 是否有效
   */
  public static verifySignature(signature: string, params: SignatureParams): boolean {
    const expectedSignature = this.generateSignature(params);
    return signature.toUpperCase() === expectedSignature;
  }
}
