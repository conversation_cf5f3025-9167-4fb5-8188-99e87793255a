/**
 * 通用类型定义
 * 定义当贝AI API中使用的基础数据类型和接口
 */

/**
 * API响应的基础结构
 * 所有当贝AI API都遵循这个响应格式
 */
export interface BaseApiResponse<T = unknown> {
  /** 请求是否成功 */
  success: boolean;
  /** 错误代码，成功时为null */
  errCode: string | null;
  /** 错误消息，成功时为null */
  errMessage: string | null;
  /** 请求唯一标识符 */
  requestId: string;
  /** 响应数据，类型根据具体API而定 */
  data: T;
}

/**
 * 设备信息配置
 * 用于标识客户端设备和应用信息
 */
export interface DeviceConfig {
  /** 设备唯一标识符 */
  deviceId: string;
  /** 应用类型，固定为6 */
  appType: number;
  /** 应用版本号 */
  appVersion: string;
  /** 客户端版本号 */
  clientVersion: string;
  /** 语言设置，默认为'zh' */
  lang: string;
  /** 用户代理字符串 */
  userAgent: string;
}

/**
 * 请求签名参数
 * 用于生成API请求的安全签名
 */
export interface SignatureParams {
  /** 时间戳（秒） */
  timestamp: number;
  /** 随机字符串 */
  nonce: string;
  /** 设备ID */
  deviceId: string;
  /** 请求体数据（可选） */
  data?: Record<string, unknown>;
  /** HTTP 方法（可选，用于规范化签名串） */
  method?: string;
  /** 请求路径（可选，如 /ai-search/...，用于规范化签名串） */
  url?: string;
}

/**
 * HTTP请求配置选项
 */
export interface RequestConfig {
  /** 请求超时时间（毫秒） */
  timeout?: number;
  /** 重试次数 */
  retries?: number;
  /** 重试延迟（毫秒） */
  retryDelay?: number;
}

/**
 * 错误类型枚举
 */
export enum ErrorType {
  /** 网络错误 */
  NETWORK_ERROR = 'NETWORK_ERROR',
  /** API错误 */
  API_ERROR = 'API_ERROR',
  /** 签名错误 */
  SIGNATURE_ERROR = 'SIGNATURE_ERROR',
  /** 参数错误 */
  PARAMETER_ERROR = 'PARAMETER_ERROR',
  /** 超时错误 */
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  /** 未知错误 */
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * 自定义错误类
 */
export class DangbeiApiError extends Error {
  public readonly type: ErrorType;
  public readonly code?: string;
  public readonly requestId?: string;

  constructor(
    message: string,
    type: ErrorType = ErrorType.UNKNOWN_ERROR,
    code?: string,
    requestId?: string
  ) {
    super(message);
    this.name = 'DangbeiApiError';
    this.type = type;
    if (code !== undefined) {
      this.code = code;
    }
    if (requestId !== undefined) {
      this.requestId = requestId;
    }
  }
}
